/* ========================================
   STYLE FUTURISTE MODERNE WHATSAPP - CODE ANCIEN OPTIMISÉ
   ======================================== */

/* Variables CSS futuristes */
:root {
  /* Couleurs principales modernes */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --success-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

  /* Couleurs neutres modernes */
  --modern-white: #ffffff;
  --modern-light: #f8fafc;
  --modern-gray-100: #f1f5f9;
  --modern-gray-200: #e2e8f0;
  --modern-gray-300: #cbd5e1;
  --modern-gray-400: #94a3b8;
  --modern-gray-500: #64748b;
  --modern-gray-600: #475569;
  --modern-gray-700: #334155;
  --modern-gray-800: #1e293b;
  --modern-gray-900: #0f172a;

  /* Mode sombre moderne */
  --dark-primary: #1a1a2e;
  --dark-secondary: #16213e;
  --dark-accent: #0f3460;
  --dark-surface: #252545;
  --dark-text: #e2e8f0;

  /* Effets modernes */
  --glass-effect: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --shadow-modern: 0 8px 32px rgba(0, 0, 0, 0.12);
  --shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.15);
  --blur-effect: blur(20px);

  /* Couleurs fluo pour icônes en mode sombre */
  --neon-cyan: #00ffff;
  --neon-pink: #ff00ff;
  --neon-green: #00ff00;
  --neon-orange: #ff6600;
  --neon-purple: #9900ff;
  --neon-yellow: #ffff00;
  --neon-blue: #0099ff;
  --neon-red: #ff3366;

  /* Dégradés fluo */
  --neon-gradient-1: linear-gradient(135deg, #00ffff 0%, #ff00ff 100%);
  --neon-gradient-2: linear-gradient(135deg, #00ff00 0%, #ffff00 100%);
  --neon-gradient-3: linear-gradient(135deg, #ff6600 0%, #ff3366 100%);
  --neon-gradient-4: linear-gradient(135deg, #9900ff 0%, #0099ff 100%);

  /* Effets de lueur fluo */
  --neon-glow-cyan: 0 0 20px #00ffff, 0 0 40px #00ffff, 0 0 60px #00ffff;
  --neon-glow-pink: 0 0 20px #ff00ff, 0 0 40px #ff00ff, 0 0 60px #ff00ff;
  --neon-glow-green: 0 0 20px #00ff00, 0 0 40px #00ff00, 0 0 60px #00ff00;
  --neon-glow-orange: 0 0 20px #ff6600, 0 0 40px #ff6600, 0 0 60px #ff6600;

  /* Variables pour le thème */
  --accent-color: #4f5fad;
  --text-light: #ffffff;
  --text-dim: #b0b0b0;
  --medium-bg: rgba(0, 0, 0, 0.3);
  --transition-fast: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --border-radius-sm: 8px;
}

/* ========================================
   CONTENEUR PRINCIPAL CHAT - DEMI-ÉCRAN OPTIMISÉ
   ======================================== */

.futuristic-chat-container {
  height: 70vh;
  max-height: 70vh;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: var(--shadow-modern);
  backdrop-filter: var(--blur-effect);
  border: 1px solid var(--glass-border);
  position: relative;
  background: var(--dark-primary);
  background-image: radial-gradient(
      circle at 25% 25%,
      rgba(102, 126, 234, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 75% 75%,
      rgba(245, 87, 108, 0.15) 0%,
      transparent 50%
    );
}

/* Effet holographique sur le conteneur */
.futuristic-chat-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(0, 247, 255, 0.1) 50%,
    transparent 70%
  );
  background-size: 200% 200%;
  animation: holographicScan 8s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes holographicScan {
  0%,
  100% {
    background-position: 0% 0%;
    opacity: 0;
  }
  50% {
    background-position: 100% 100%;
    opacity: 1;
  }
}

/* ========================================
   EN-TÊTE CHAT FUTURISTE
   ======================================== */

.whatsapp-chat-header {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: var(--primary-gradient);
  background-size: 200% 200%;
  animation: gradientShift 8s ease infinite;
  border-bottom: 1px solid var(--glass-border);
  height: 60px;
  backdrop-filter: var(--blur-effect);
  box-shadow: var(--shadow-modern);
  position: relative;
  overflow: hidden;
  z-index: 2;
}

/* Animation de dégradé pour l'en-tête */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

:host-context(.dark) .whatsapp-chat-header {
  background: var(--dark-secondary);
  background-image: var(--accent-gradient);
  background-size: 200% 200%;
  border-bottom: 1px solid var(--dark-accent);
}

.whatsapp-user-info {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: 12px;
}

.whatsapp-avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
}

.whatsapp-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.whatsapp-online-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #25d366;
  border: 2px solid #f0f2f5;
}

:host-context(.dark) .whatsapp-online-indicator {
  border-color: #2a2a2a;
}

.whatsapp-user-details {
  margin-left: 8px;
  display: flex;
  flex-direction: column;
}

.whatsapp-username {
  font-size: 0.9375rem;
  font-weight: 600;
  color: #333;
}

:host-context(.dark) .whatsapp-username {
  color: #e0e0e0;
}

.whatsapp-status {
  font-size: 0.75rem;
  color: #666;
}

:host-context(.dark) .whatsapp-status {
  color: #aaa;
}

.whatsapp-actions {
  display: flex;
  gap: 16px;
}

/* ========================================
   BOUTONS D'ACTION FUTURISTES - STYLE UNIFORME
   ======================================== */

.whatsapp-action-button {
  background: transparent;
  border: none;
  color: #00f7ff;
  width: 42px;
  height: 42px;
  border-radius: 0; /* Suppression de toute forme géométrique */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  font-size: 1.1rem;
  /* Style épuré sans bordures ni arrière-plan */
}

.whatsapp-action-button:hover {
  transform: scale(1.15) translateY(-3px);
  box-shadow: 0 0 30px rgba(0, 247, 255, 0.6), 0 0 60px rgba(255, 20, 147, 0.3),
    0 10px 30px rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 247, 255, 0.8);
  color: #ffffff;
  text-shadow: 0 0 10px rgba(0, 247, 255, 0.8);
}

/* Boutons d'appel et vidéo gardent leur forme circulaire */
.whatsapp-action-button.btn-audio-call,
.whatsapp-action-button.btn-video-call {
  border-radius: 50% !important; /* Garder la forme circulaire pour les appels */
  background: rgba(0, 247, 255, 0.1) !important;
  border: 1px solid rgba(0, 247, 255, 0.3) !important;
}

/* Mode sombre avec couleurs fluo */
:host-context(.dark) .whatsapp-action-button {
  background: var(--glass-effect);
  border: 1px solid var(--neon-cyan);
  color: var(--neon-cyan);
  text-shadow: 0 0 10px var(--neon-cyan);
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

:host-context(.dark) .whatsapp-action-button:hover {
  background: var(--neon-gradient-1);
  color: white;
  text-shadow: none;
  box-shadow: var(--neon-glow-cyan);
  border-color: transparent;
}

/* ========================================
   ZONE DE MESSAGES FUTURISTE
   ======================================== */

.futuristic-messages-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: transparent;
  position: relative;
  z-index: 2;
}

/* Style pour les scrollbars futuristes */
.futuristic-messages-container::-webkit-scrollbar {
  width: 5px;
}

.futuristic-messages-container::-webkit-scrollbar-track {
  background: rgba(0, 247, 255, 0.05);
}

.futuristic-messages-container::-webkit-scrollbar-thumb {
  background-color: var(--accent-color);
  border-radius: 10px;
  border: transparent;
}

/* ========================================
   MESSAGES STYLE WHATSAPP FUTURISTE
   ======================================== */

/* Wrapper de message futuriste */
.futuristic-message-wrapper {
  margin-bottom: 4px;
  position: relative;
  z-index: 1;
}

/* Message futuriste style WhatsApp */
.futuristic-message {
  display: flex;
  align-items: flex-end;
  margin-bottom: 1px;
  position: relative;
  width: 100%;
}

/* Bulle de message futuriste style WhatsApp amélioré */
.futuristic-message-bubble {
  margin-bottom: 0.25rem;
  max-width: 70%;
  min-width: 40px;
  padding: 0.6rem 0.8rem;
  font-size: 0.9rem;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: normal;
  display: inline-block;
  width: auto;
  white-space: normal;
  overflow-wrap: break-word;
  text-align: left;
  direction: ltr;
  position: relative;
  transition: all var(--transition-fast);
  animation: fadeIn 0.3s ease-out;
  border-radius: 12px;
  letter-spacing: 0.02em;
  font-weight: 400;
  backdrop-filter: blur(5px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Style pour le contenu du message */
.futuristic-message-content {
  max-width: 80%;
}

.futuristic-message-text {
  white-space: normal;
  word-break: break-word;
  overflow-wrap: break-word;
  text-align: left;
  direction: ltr;
  min-width: 80px;
  font-weight: 400;
  letter-spacing: 0.01em;
  line-height: 1.5;
}

/* Style pour les messages de l'utilisateur actuel (style WhatsApp) */
.futuristic-message-current-user {
  justify-content: flex-end;
  width: 100%;
  display: flex;
  margin-left: auto;
  animation: slideInRight 0.3s ease-out;
}

.futuristic-message-current-user .futuristic-message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  max-width: 80%;
  margin-left: auto;
}

.futuristic-message-current-user .futuristic-message-bubble {
  background: linear-gradient(135deg, rgba(0, 247, 255, 0.9), rgba(131, 56, 236, 0.9));
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 247, 255, 0.4);
  backdrop-filter: blur(10px);
  transform: perspective(800px) rotateX(0deg);
  transition: all 0.3s ease;
  border-radius: 12px;
  margin-left: auto;
  position: relative;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.futuristic-message-current-user .futuristic-message-bubble:hover {
  transform: perspective(800px) rotateX(2deg) translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 247, 255, 0.5);
}

/* Flèche style WhatsApp pour les messages de l'utilisateur actuel */
.futuristic-message-current-user .futuristic-message-bubble::before {
  content: "";
  position: absolute;
  top: 0;
  right: -8px;
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-left: 8px solid rgba(131, 56, 236, 0.8);
  border-bottom: 8px solid transparent;
  z-index: 1;
  filter: drop-shadow(2px 0px 2px rgba(0, 0, 0, 0.1));
}

.futuristic-message-current-user .futuristic-message-text {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Style pour les messages des autres utilisateurs (style WhatsApp) */
.futuristic-message-other-user {
  justify-content: flex-start;
  width: 100%;
  display: flex;
  margin-right: auto;
  animation: slideInLeft 0.3s ease-out;
}

.futuristic-message-other-user .futuristic-message-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: 80%;
  margin-right: auto;
}

/* Style pour les messages des autres utilisateurs en mode clair */
:host-context(:not(.dark))
  .futuristic-message-other-user
  .futuristic-message-bubble {
  background: var(--modern-white);
  background-image: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.05) 0%,
    rgba(245, 87, 108, 0.05) 100%
  );
  color: var(--modern-gray-800);
  border-radius: 18px 18px 18px 4px;
  margin-right: auto;
  box-shadow: var(--shadow-modern);
  border: 1px solid var(--modern-gray-200);
  position: relative;
  backdrop-filter: var(--blur-effect);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Style pour les messages des autres utilisateurs en mode sombre */
:host-context(.dark) .futuristic-message-other-user .futuristic-message-bubble {
  background: var(--dark-surface);
  background-image: linear-gradient(
    135deg,
    rgba(79, 172, 254, 0.1) 0%,
    rgba(245, 87, 108, 0.1) 100%
  );
  color: var(--dark-text);
  border-radius: 18px 18px 18px 4px;
  margin-right: auto;
  box-shadow: var(--shadow-modern);
  border: 1px solid var(--dark-accent);
  position: relative;
  backdrop-filter: var(--blur-effect);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.futuristic-message-other-user .futuristic-message-text {
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

/* Animations pour les messages */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Heure du message futuriste */
.futuristic-message-time {
  font-size: 0.7rem;
  margin-top: 0.2rem;
  opacity: 0.7;
  color: var(--text-dim);
}

/* Informations du message futuriste */
.futuristic-message-info {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 6px;
  margin-top: 4px;
  font-size: 0.75rem;
  letter-spacing: 0.02em;
  font-weight: 300;
}

.futuristic-message-current-user .futuristic-message-info {
  color: rgba(255, 255, 255, 0.8);
}

.futuristic-message-other-user .futuristic-message-info {
  color: rgba(0, 247, 255, 0.7);
}

.futuristic-message-status {
  color: rgba(0, 247, 255, 0.9);
}

/* ========================================
   ZONE DE SAISIE FUTURISTE WHATSAPP
   ======================================== */

/* Conteneur d'entrée futuriste parfait */
.futuristic-input-container {
  padding: 12px 20px;
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(0, 247, 255, 0.2);
  backdrop-filter: blur(15px);
  position: relative;
  z-index: 2;
  min-height: 70px;
}

.futuristic-input-form {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.futuristic-input-tools {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* Champ de saisie futuriste parfait */
.futuristic-input-field,
.whatsapp-input {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(0, 247, 255, 0.2);
  border-radius: 25px;
  color: #ffffff;
  padding: 12px 20px;
  font-size: 0.95rem;
  outline: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(5px);
  flex: 1;
}

.futuristic-input-field:focus,
.whatsapp-input:focus {
  border-color: #00f7ff;
  box-shadow: 0 0 15px rgba(0, 247, 255, 0.3);
  background: rgba(255, 255, 255, 0.12);
}

.futuristic-input-field::placeholder,
.whatsapp-input::placeholder {
  color: #b0b0b0;
  opacity: 0.7;
}

/* ========================================
   BOUTONS D'OUTILS FUTURISTES
   ======================================== */

.futuristic-tool-button,
.whatsapp-tool-button {
  width: 40px;
  height: 40px;
  background: transparent;
  border: none;
  color: #00f7ff;
  border-radius: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  text-shadow: 0 0 8px currentColor;
}

.futuristic-tool-button:hover,
.whatsapp-tool-button:hover {
  color: #ffffff;
  transform: scale(1.1) translateY(-2px);
  text-shadow: 0 0 12px currentColor, 0 0 24px currentColor;
}

.futuristic-tool-button.active,
.whatsapp-tool-button.active {
  color: #ffffff;
  text-shadow: 0 0 15px #00f7ff;
}

/* ========================================
   BOUTON VOCAL FUTURISTE INNOVANT - STYLE WHATSAPP
   ======================================== */

.whatsapp-voice-button {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #ff4757, #ff6b7a);
  border: none;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.3rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(255, 71, 87, 0.4);
}

/* Effet de pulsation pour l'enregistrement */
.whatsapp-voice-button.recording {
  animation: recordingPulse 1.5s ease-in-out infinite;
  background: linear-gradient(135deg, #ff3742, #ff5722);
  box-shadow: 0 0 25px rgba(255, 71, 87, 0.8);
}

@keyframes recordingPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Onde sonore animée pendant l'enregistrement */
.whatsapp-voice-button.recording::before {
  content: "";
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border: 2px solid rgba(255, 71, 87, 0.6);
  border-radius: 50%;
  animation: soundWave 2s ease-out infinite;
}

.whatsapp-voice-button.recording::after {
  content: "";
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border: 1px solid rgba(255, 71, 87, 0.3);
  border-radius: 50%;
  animation: soundWave 2s ease-out infinite 0.5s;
}

@keyframes soundWave {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.whatsapp-voice-button:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(255, 71, 87, 0.6);
}

/* ========================================
   BOUTON D'ENVOI FUTURISTE
   ======================================== */

.futuristic-send-button,
.whatsapp-send-button {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #00f7ff, #1e90ff);
  border: none;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.3rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 247, 255, 0.4);
}

.futuristic-send-button:hover,
.whatsapp-send-button:hover {
  transform: scale(1.05) rotate(15deg);
  box-shadow: 0 6px 20px rgba(0, 247, 255, 0.6);
}

.futuristic-send-button:disabled,
.whatsapp-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* ========================================
   MESSAGES VOCAUX FUTURISTES PARFAITS
   ======================================== */

.voice-message-modern {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(0, 247, 255, 0.2);
  border-radius: 20px;
  padding: 12px 16px;
  min-width: 200px;
  backdrop-filter: blur(5px);
}

.voice-play-btn-modern,
.play-button {
  width: 35px;
  height: 35px;
  background: #00f7ff;
  border: none;
  border-radius: 50%;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
}

.voice-play-btn-modern:hover,
.play-button:hover {
  background: #1e90ff;
  transform: scale(1.1);
}

.voice-play-btn-modern.playing,
.play-button.playing {
  color: #ff4500;
}

.voice-waveform {
  flex: 1;
  height: 30px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.voice-bar {
  width: 3px;
  background: #00f7ff;
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.6;
}

.voice-bar.active {
  opacity: 1;
  box-shadow: 0 0 5px #00f7ff;
}

.voice-duration {
  color: #b0b0b0;
  font-size: 0.8rem;
  font-weight: 500;
  flex-shrink: 0;
}

/* ========================================
   RESPONSIVITÉ DEMI-ÉCRAN OPTIMISÉE
   ======================================== */

/* Optimisations pour les très petits écrans */
@media (max-width: 480px) {
  .futuristic-message {
    max-width: 90% !important;
  }

  .voice-message-modern {
    min-width: 150px !important;
    max-width: 250px !important;
  }

  .whatsapp-input {
    font-size: 14px !important;
  }

  .whatsapp-username {
    font-size: 0.85rem !important;
  }

  .whatsapp-status {
    font-size: 0.7rem !important;
  }
}

/* ========================================
   FIN DU FICHIER CSS - CODE ANCIEN OPTIMISÉ SANS DUPLICATION
   ======================================== */
