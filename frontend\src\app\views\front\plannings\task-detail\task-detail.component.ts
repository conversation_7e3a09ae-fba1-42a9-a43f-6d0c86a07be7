import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TaskService } from '../../../../services/task.service';
import { Task } from '../../../../models/task.model';

@Component({
  selector: 'app-task-detail',
  templateUrl: './task-detail.component.html',
  styleUrls: ['./task-detail.component.css']
})
export class TaskDetailComponent implements OnInit {
  task: Task | null = null;
  loading = true;
  error: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private taskService: TaskService
  ) {}

  ngOnInit(): void {
    this.loadTask();
  }

  private loadTask(): void {
    const taskId = this.route.snapshot.paramMap.get('id');
    if (!taskId) {
      this.error = 'ID de tâche manquant';
      this.loading = false;
      return;
    }

    this.taskService.getTaskById(taskId).subscribe({
      next: (task) => {
        this.task = task;
        this.loading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement de la tâche:', error);
        this.error = 'Erreur lors du chargement de la tâche';
        this.loading = false;
      }
    });
  }

  goBack(): void {
    this.router.navigate(['/plannings']);
  }

  editTask(): void {
    if (this.task?._id) {
      this.router.navigate(['/plannings/edit', this.task._id]);
    }
  }
}
